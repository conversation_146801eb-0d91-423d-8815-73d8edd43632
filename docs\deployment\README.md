# DL引擎部署指南

本目录包含DL引擎的完整部署文档和配置文件。

## 📁 文件说明

### 部署文档
- **`docker-compose.md`** - 详细的Docker Compose部署指南
- **`kubernetes.md`** - Kubernetes部署指南（原版）
- **`kubernetes-complete.md`** - Kubernetes完整部署指南
- **`kubernetes-deployment-guide.md`** - Kubernetes快速部署指南
- **`README.md`** - 本文件

### 配置文件
- **`docker-compose-complete.yml`** - 完整的Docker Compose配置文件
- **`docker-compose-monitoring.yml`** - 监控服务配置文件
- **`.env.example`** - 环境变量配置示例

### 部署脚本
- **`deploy.sh`** - Docker Compose一键部署脚本
- **`k8s-deploy.sh`** - Kubernetes自动化部署脚本

### Kubernetes配置
- **`k8s/service-registry.yaml`** - 服务注册中心配置
- **`k8s/user-service.yaml`** - 用户服务配置
- **`k8s/api-gateway.yaml`** - API网关配置
- **`k8s/ingress.yaml`** - Ingress配置
- **`k8s/monitoring.yaml`** - 监控配置

## 🚀 快速开始

### Docker Compose 部署（推荐用于开发和测试）

#### 方法一：使用一键部署脚本

```bash
# 1. 进入项目根目录
cd newsystem

# 2. 运行部署脚本
./docs/deployment/deploy.sh
```

脚本会自动：
- 检查系统要求
- 创建环境变量文件
- 构建和启动所有服务
- 验证部署状态

#### 方法二：手动部署

```bash
# 1. 复制环境变量文件
cp docs/deployment/.env.example .env

# 2. 编辑环境变量（重要！）
nano .env

# 3. 使用完整配置启动
docker-compose -f docs/deployment/docker-compose-complete.yml up -d

# 4. 等待服务启动完成
./scripts/check-services.sh
```

### Kubernetes 部署（推荐用于生产环境）

#### 方法一：使用自动化脚本

```bash
# 1. 下载部署脚本
curl -O https://raw.githubusercontent.com/your-repo/dl-engine/main/docs/deployment/k8s-deploy.sh

# 2. 配置脚本
chmod +x k8s-deploy.sh
# 编辑脚本中的容器仓库地址

# 3. 执行部署
./k8s-deploy.sh

# 4. 部署微服务
kubectl apply -f docs/deployment/k8s/
```

#### 方法二：手动部署

```bash
# 1. 创建命名空间
kubectl create namespace dl-engine

# 2. 构建并推送镜像
# 参考 kubernetes-deployment-guide.md

# 3. 部署基础设施（使用Helm）
helm install mysql bitnami/mysql --namespace dl-engine
helm install redis bitnami/redis --namespace dl-engine
helm install minio bitnami/minio --namespace dl-engine

# 4. 部署微服务
kubectl apply -f docs/deployment/k8s/service-registry.yaml
kubectl apply -f docs/deployment/k8s/user-service.yaml
kubectl apply -f docs/deployment/k8s/api-gateway.yaml
kubectl apply -f docs/deployment/k8s/ingress.yaml

# 5. 验证部署
kubectl get pods -n dl-engine
```

## 📋 系统要求

### 硬件要求
- **CPU**: 4核心以上（推荐8核心）
- **内存**: 8GB以上（推荐16GB）
- **存储**: 50GB以上可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **操作系统**: Linux、macOS或Windows

### 端口要求
确保以下端口未被占用：
- **80** - 编辑器前端
- **3000** - API网关
- **3001-3007** - 微服务
- **3010** - 服务注册中心
- **3030** - 游戏服务器
- **3306** - MySQL
- **6379** - Redis
- **9000-9001** - MinIO

## 🏗️ 系统架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   编辑器     │    │  API网关     │    │  微服务集群  │
│  (端口:80)   │───▶│ (端口:3000)  │───▶│ (端口:3001+) │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                           ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   MySQL     │    │    Redis    │    │   MinIO     │
│ (端口:3306)  │    │ (端口:6379)  │    │ (端口:9000)  │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 🔧 核心服务

### 基础设施层
- **MySQL 8.0** - 主数据存储
- **Redis 7.0** - 缓存和会话存储
- **MinIO** - 对象存储服务

### 微服务层
- **服务注册中心** - 服务发现和注册
- **API网关** - 统一入口和路由
- **用户服务** - 用户管理和认证
- **项目服务** - 项目和场景管理
- **资产服务** - 资产文件管理
- **渲染服务** - 3D渲染和图像处理
- **协作服务** - 实时协作功能

### 前端应用
- **编辑器** - 基于React的可视化编辑器

## 🌐 访问地址

部署完成后，可通过以下地址访问：

- **编辑器主页**: http://localhost
- **API网关**: http://localhost:3000/api
- **API文档**: http://localhost:3000/api/docs
- **MinIO控制台**: http://localhost:9001

## 📊 监控和管理

### 查看服务状态
```bash
# 查看所有容器状态
docker-compose ps

# 查看服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f api-gateway
```

### 健康检查
```bash
# 使用检查脚本
./scripts/check-services.sh

# 手动检查API网关
curl http://localhost:3000/api/health

# 手动检查各微服务
curl http://localhost:4001/health  # 用户服务
curl http://localhost:4002/health  # 项目服务
curl http://localhost:4003/health  # 资产服务
curl http://localhost:4004/health  # 渲染服务
```

### 服务管理
```bash
# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart api-gateway

# 查看资源使用情况
docker stats

# 清理未使用的资源
docker system prune -f
```

## 🔒 安全配置

### 必须修改的配置
1. **数据库密码** - `MYSQL_ROOT_PASSWORD`
2. **JWT密钥** - `JWT_SECRET`
3. **MinIO密钥** - `MINIO_ROOT_PASSWORD`
4. **Redis密码** - `REDIS_PASSWORD`

### 生成安全密钥
```bash
# 生成随机密码
openssl rand -base64 32

# 生成JWT密钥
openssl rand -hex 64
```

### 文件权限
```bash
# 设置.env文件权限（仅所有者可读写）
chmod 600 .env

# 设置数据目录权限
chmod -R 755 data/
```

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :3000
   
   # 修改端口配置
   # 编辑.env文件中的端口配置
   ```

2. **容器启动失败**
   ```bash
   # 查看容器日志
   docker-compose logs <service-name>
   
   # 重新构建容器
   docker-compose build <service-name>
   docker-compose up -d <service-name>
   ```

3. **数据库连接失败**
   ```bash
   # 检查MySQL容器状态
   docker-compose ps mysql
   
   # 查看MySQL日志
   docker-compose logs mysql
   
   # 重置数据库
   docker-compose down
   docker volume rm dl-engine-mysql-data
   docker-compose up -d mysql
   ```

4. **服务注册失败**
   ```bash
   # 检查服务注册中心
   curl http://localhost:4010/health
   
   # 查看注册的服务
   curl http://localhost:4010/api/services
   ```

### 获取帮助

1. 查看详细文档：`docs/deployment/docker-compose.md`
2. 检查项目Issues：GitHub Issues页面
3. 联系技术支持团队

## 📚 相关文档

### 部署文档
- [Docker Compose详细部署指南](./docker-compose.md)
- [Kubernetes部署指南（原版）](./kubernetes.md)
- [Kubernetes完整部署指南](./kubernetes-complete.md)
- [Kubernetes快速部署指南](./kubernetes-deployment-guide.md)

### 其他文档
- [API文档](../api/README.md)
- [开发者指南](../developer/README.md)
- [用户手册](../user-manual/README.md)
- [架构设计](../developer/architecture/README.md)

## 📝 更新日志

- **v1.0.0** - 初始版本，包含完整的Docker Compose部署配置
- **v1.1.0** - 添加MinIO对象存储支持
- **v1.2.0** - 增加协作服务负载均衡
- **v1.3.0** - 添加游戏服务器支持
- **v1.4.0** - 完善监控和日志配置

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。
